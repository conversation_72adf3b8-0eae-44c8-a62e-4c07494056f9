import React, { useState } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Car<PERSON>ianG<PERSON>,
  <PERSON><PERSON><PERSON>,
  ResponsiveContainer,
} from "recharts";
import { BASE_COLORS_PIE } from "../../common/constants";

// Utility: assign unique, stable colors to categories
function generateUniqueColors(count, existingColors = []) {
  const colors = [];

  // Completely unrelated colors - no similar hues or families
  const distinctColors = [
    "#B71C1C", // Deep Red
    "#0D47A1", // Deep Blue
    "#1B5E20", // Deep Green
    "#E65100", // Deep Orange
    "#4A148C", // Deep Purple
    "#006064", // Deep Teal
    "#3E2723", // Deep Brown
    "#263238", // Blue Grey
    "#880E4F", // Deep Pink
    "#33691E", // Light Green
    "#01579B", // Light Blue
    "#BF360C", // Deep Orange Red
    "#1A237E", // Indigo
    "#004D40", // <PERSON><PERSON>
    "#F57F17", // <PERSON>
    "#827717", // Lime
    "#5D4037", // <PERSON>
    "#37474F", // Blue Grey
    "#C62828", // Red
    "#1565C0", // Blue
    "#2E7D32", // Green
    "#EF6C00", // Orange
    "#6A1B9A", // Purple
    "#00838F", // Cyan
    "#8D6E63", // Brown
    "#546E7A", // Blue Grey
    "#AD1457", // Pink
    "#558B2F", // Light Green
    "#1976D2", // Blue
    "#D84315", // Deep Orange
  ];

  // Use distinct colors first
  for (let i = 0; i < Math.min(count, distinctColors.length); i++) {
    if (!existingColors.includes(distinctColors[i])) {
      colors.push(distinctColors[i]);
      existingColors.push(distinctColors[i]);
    }
  }

  // If we need more colors, generate them with maximum hue separation
  const remainingCount = count - colors.length;
  if (remainingCount > 0) {
    // Use golden ratio for maximum separation
    const goldenRatio = 137.508; // Golden angle in degrees
    let baseHue = 0;

    for (let i = 0; i < remainingCount; i++) {
      let attempts = 0;
      let newColor;

      do {
        // Use golden ratio for maximum hue separation
        const hue = (baseHue + i * goldenRatio + attempts * 60) % 360;
        // Alternate between different saturation and lightness levels
        const sat = i % 2 === 0 ? 85 : 75;
        const light = i % 3 === 0 ? 30 : i % 3 === 1 ? 40 : 35;

        newColor = `hsl(${Math.round(hue)}, ${sat}%, ${light}%)`;
        attempts++;
      } while (existingColors.includes(newColor) && attempts < 50);

      colors.push(newColor);
      existingColors.push(newColor);
    }
  }

  return colors;
}

function getColorMap(data, colorBy) {
  const uniqueValues = [...new Set(data.map((d) => d[colorBy]))];
  const map = {};

  // Use a smaller subset of base colors to avoid similar colors, then generate distinct ones
  const selectedBaseColors = BASE_COLORS_PIE.slice(
    0,
    Math.min(8, uniqueValues.length)
  ); // Limit base colors

  const neededColors = Math.max(
    0,
    uniqueValues.length - selectedBaseColors.length
  );
  const additionalColors =
    neededColors > 0
      ? generateUniqueColors(neededColors, [...selectedBaseColors])
      : [];

  uniqueValues.forEach((val, idx) => {
    if (idx < selectedBaseColors.length) {
      map[val] = selectedBaseColors[idx];
    } else {
      map[val] = additionalColors[idx - selectedBaseColors.length];
    }
  });

  return map;
}

// Custom Legend Component similar to BarChartGraph
const CustomLegend = ({ payload = [], hiddenItems, onToggleItem }) => {
  const [showFullLegend, setShowFullLegend] = useState(false);

  const visibleItems = showFullLegend ? payload : payload.slice(0, 12);
  const visibleItemCount = payload.length - hiddenItems.size;

  return (
    <div className="bg-white border border-gray-400 rounded-lg p-3 m-5">
      <div className="flex flex-wrap gap-6 items-center justify-start">
        {visibleItems.map((entry, index) => {
          const isHidden = hiddenItems.has(entry.id);
          const canHide = visibleItemCount > 1;
          const canClick = isHidden || canHide;

          return (
            <div
              key={index}
              onClick={() => canClick && onToggleItem(entry.id)}
              className={`
                flex items-center gap-2 transition-all duration-200
                ${canClick ? "cursor-pointer" : "cursor-not-allowed opacity-70"}
                ${canClick ? "hover:opacity-80" : ""}
              `}
            >
              <span
                className={`
                  w-3 h-3 rounded-full flex-shrink-0
                  ${isHidden ? "opacity-30" : ""}
                `}
                style={{
                  backgroundColor: entry.color,
                }}
              />
              <span
                className="text-xs font-medium"
                style={{
                  color: entry.color,
                  textDecoration: isHidden ? "line-through" : "none",
                }}
              >
                {entry.value}
              </span>
            </div>
          );
        })}
      </div>
      {payload.length > 12 && (
        <div className="mt-3 text-center">
          <button
            onClick={() => setShowFullLegend((prev) => !prev)}
            className="text-blue-600 hover:text-blue-800 transition-colors text-xs font-medium"
          >
            {showFullLegend ? "Show Less ▲" : "Show All ▼"}
          </button>
        </div>
      )}
    </div>
  );
};

export default function ScatterPlotChartGraph({ data, config }) {
  const { xAxis, yAxis, bubbleSize, colorBy } = config;
  const [hiddenItems, setHiddenItems] = useState(new Set());

  // Reset hidden items when config changes
  React.useEffect(() => {
    setHiddenItems(new Set());
  }, [xAxis, yAxis, bubbleSize, colorBy]);

  // If there's no data, show a friendly message (matches LineChartGraph behavior)
  if (!Array.isArray(data) || data.length === 0) {
    return (
      <div className="w-full h-60 flex justify-center items-center text-gray-500 font-medium">
        No data to display
      </div>
    );
  }

  // Process data to handle empty strings in colorBy field only
  const processedData = data.map((item) => {
    const processedItem = { ...item };

    // Handle empty string in colorBy field only
    if (processedItem[colorBy] === "") {
      processedItem[colorBy] = "Empty String";
    }

    return processedItem;
  });

  // Generate color mapping dynamically from processed data
  const colorMap = getColorMap(processedData, colorBy);

  // Group processed data for legend
  const groupedData = processedData.reduce((acc, entry) => {
    const key = entry[colorBy];
    if (!acc[key]) acc[key] = [];
    acc[key].push(entry);
    return acc;
  }, {});

  const handleToggleItem = (itemKey) => {
    setHiddenItems((prev) => {
      const newSet = new Set(prev);
      const currentVisibleCount = Object.keys(groupedData).length - prev.size;
      if (newSet.has(itemKey)) {
        newSet.delete(itemKey);
      } else {
        if (currentVisibleCount > 1) {
          newSet.add(itemKey);
        }
      }
      return newSet;
    });
  };

  function formatNumber(value) {
    if (value >= 1_000_000)
      return (value / 1_000_000).toFixed(1).replace(/\.0$/, "") + "M";
    if (value >= 1_000)
      return (value / 1_000).toFixed(1).replace(/\.0$/, "") + "K";
    return value;
  }

  // Custom Tooltip similar to BarChartGraph
  const CustomTooltip = ({ active, payload, hiddenItems = new Set() }) => {
    if (!active || !payload || !payload.length) return null;

    // Filter out hidden items from tooltip
    const visiblePayload = payload.filter(
      (entry) => !hiddenItems.has(entry.payload[colorBy])
    );

    if (visiblePayload.length === 0) return null;

    const data = payload[0].payload;

    return (
      <div className="bg-white p-3 border border-gray-300 rounded-lg shadow-lg text-xs">
        <p className="mb-2 font-bold text-sm text-gray-800">{`${xAxis}: ${data[xAxis]}`}</p>
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              {/* <span
                className="w-3 h-3 rounded mr-2"
                style={{ backgroundColor: colorMap[data[colorBy]] }}
              /> */}
              <span
                className="font-medium"
                style={{ color: colorMap[data[colorBy]] }}
              >
                {yAxis}
              </span>
            </div>
            <span className="text-gray-900 font-bold ml-3">{data[yAxis]}</span>
          </div>
          {bubbleSize && (
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                {/* <span
                  className="w-3 h-3 rounded mr-2"
                  style={{ backgroundColor: colorMap[data[colorBy]] }}
                /> */}
                <span
                  className="font-medium"
                  style={{ color: colorMap[data[colorBy]] }}
                >
                  {bubbleSize}
                </span>
              </div>
              <span className="text-gray-900 font-bold ml-3">
                {data[bubbleSize]}
              </span>
            </div>
          )}
          {colorBy && (
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                {/* <span
                  className="w-3 h-3 rounded mr-2"
                  style={{ backgroundColor: colorMap[data[colorBy]] }}
                /> */}
                <span
                  className="font-medium"
                  style={{ color: colorMap[data[colorBy]] }}
                >
                  {colorBy}
                </span>
              </div>
              <span className="text-gray-900 font-bold ml-3">
                {data[colorBy]}
              </span>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <>
      <ResponsiveContainer width="100%" height={400}>
        <ScatterChart margin={{ top: 20, right: 40, bottom: 20, left: 20 }}>
          <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
          <XAxis
            type="number"
            dataKey={xAxis}
            name={xAxis}
            tickFormatter={formatNumber}
            tick={{
              fontSize: 11,
              fill: "#374151",
              fontWeight: "bold",
            }}
            axisLine={{
              stroke: "#374151",
              strokeWidth: 2,
              opacity: 0.2,
            }}
            tickLine={{
              stroke: "#374151",
              strokeWidth: 1,
              opacity: 0.6,
            }}
            label={{
              value: xAxis,
              position: "insideBottom",
              offset: -10,
              style: {
                textAnchor: "middle",
                fill: "#374151",
                fontSize: "11px",
                fontWeight: "bold",
              },
            }}
          />
          <YAxis
            type="number"
            dataKey={yAxis}
            name={yAxis}
            orientation="left"
            tickFormatter={formatNumber}
            tick={{
              fontSize: 10,
              fill: "#374151",
              fontWeight: "bold",
            }}
            axisLine={{
              stroke: "#374151",
              strokeWidth: 2,
              opacity: 0.2,
            }}
            tickLine={{
              stroke: "#374151",
              strokeWidth: 1,
              opacity: 0.6,
            }}
            label={{
              value: yAxis,
              angle: -90,
              position: "insideLeft",
              offset: 20,
              style: {
                textAnchor: "middle",
                fill: "#374151",
                fontSize: "11px",
                fontWeight: "bold",
              },
            }}
          />
          {bubbleSize && (
            <ZAxis
              type="number"
              dataKey={bubbleSize}
              range={[50, 400]}
              name={bubbleSize}
            />
          )}
          <Tooltip
            cursor={{ strokeDasharray: "3 3" }}
            content={<CustomTooltip hiddenItems={hiddenItems} />}
            wrapperStyle={{ zIndex: 1000, outline: "none" }}
          />
          {/* Render scatter groups */}
          {Object.keys(groupedData)
            .filter((key) => !hiddenItems.has(key))
            .map((key) => (
              <Scatter
                key={key}
                name={key}
                data={groupedData[key]}
                fill={colorMap[key]}
              />
            ))}
        </ScatterChart>
      </ResponsiveContainer>
      <CustomLegend
        payload={Object.keys(groupedData).map((key) => ({
          id: key,
          value: key,
          color: colorMap[key],
        }))}
        hiddenItems={hiddenItems}
        onToggleItem={handleToggleItem}
      />
    </>
  );
}
