import React, {
  useContext,
  useRef,
  useState,
  useMemo,
  useCallback,
  useEffect,
} from "react";
import { Formik, Form, Field } from "formik";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from "@mui/material";
import CustomDropDown from "../../components/Dropdown/ReportsDropdownList";
import InputLabel from "../FormsUI/InputLabel/InputLabel";
import Button from "../../components/Button/Button";
import OutlinedButton from "../../components/Button/OutlinedButton";
import TextField from "../FormsUI/TextField";
import { MetaDataContext } from "../../context/MetaDataContext";
import { useMutation, useQuery } from "react-query";
import { reportSave, reportService } from "../../services/staticreport.service";
import { DataContext } from "../../context/DataContext";
import {
  BASIC_COLORS,
  FILTER_PERIOD_REPORT,
  filterLabelMap,
  smsFirewallViews,
} from "../../common/constants";
import { AuthContext } from "../../context/AuthContext";
import Select from "../../components/FormsUI/Select";
import { InfoIcon, CloseIcon } from "../../icons";
import { createValidationSchema } from "../../common/validationHelpers";
import { validateAndAlertFilters } from "../../common/filterValidationUtils";
import InfoModal from "../modals/InfoModal";
import { CssTooltip } from "../StyledComponent";
import MultiAxisDropdown from "../Dropdown/MultiAxisDropdown";
import { CHART_TYPES, FilterField } from "../../utils/chartTypeUtils";

// Constants moved outside component to prevent recreation on each render

const MULTI_AXIS_CHART_TYPES = ["line", "bar", "pie", "multiAxis"];
const CHART_TYPES_WITH_AXES = [
  "line",
  "bar",
  "gauge",
  "pie",
  "scatter",
  "multiAxis",
  "heat",
];

// Helper function moved outside component
const isValidFilterValue = (allFilters, key, value) =>
  allFilters.includes(key) &&
  value !== null &&
  value !== undefined &&
  value !== "" &&
  value !== false &&
  (!Array.isArray(value) || value.length > 0);

export default function FilterComponent(props) {
  const formikRef = useRef(null);
  const { setBilateralData } = useContext(DataContext);
  const { user } = useContext(AuthContext);
  const [availableFilters, setAvailableFilters] = useState([]);
  const [otherFilter, setOtherFilter] = useState([]);
  const [showInfoModal, setShowInfoModal] = useState(false);
  const [modalMessage, setModalMessage] = useState("");
  const metaDataContext = useContext(MetaDataContext);
  const [filterState, setFilterState] = useState(
    props?.filterData || { graphFilters: {}, filters: [] }
  );

  // Destructure with memoization to prevent unnecessary re-renders
  const {
    customers,
    customerBind,
    suppliers,
    supplierBind,
    destinationNameList,
    destinationCountryList,
    lcrDataList,
    cdrStatus,
    customerProtocol,
    supplierProtocol,
    supplierBillingLogic,
    customerBillingLogic,
    specLCRDataList,
    customerInterfaceType,
    supplierInterfaceType,
    sourcePrime,
    destinationPrime,
    customerAirtelKamList,
    supplierAirtelKamList,
    customerInterconnect,
    supplierInterconnect,
  } = metaDataContext;

  // Memoized check to determine if all metadata has loaded
  const isMetaDataLoaded = useMemo(() => {
    // Check if essential metadata arrays/objects are loaded
    const essentialData = [
      customers,
      customerBind,
      suppliers,
      supplierBind,
      destinationNameList,
      destinationCountryList,
      lcrDataList,
      cdrStatus,
      customerProtocol,
      supplierProtocol,
      supplierBillingLogic,
      customerBillingLogic,
      specLCRDataList,
      customerInterfaceType,
      supplierInterfaceType,
      sourcePrime,
      destinationPrime,
      customerAirtelKamList,
      supplierAirtelKamList,
      customerInterconnect,
      supplierInterconnect,
    ];

    // Return true only if all essential data is loaded (not null/undefined and has length > 0 for arrays)
    return essentialData.every((data) => {
      if (Array.isArray(data)) {
        return data && data.length > 0;
      }
      return data !== null && data !== undefined;
    });
  }, [
    customers,
    customerBind,
    suppliers,
    supplierBind,
    destinationNameList,
    destinationCountryList,
    lcrDataList,
    cdrStatus,
    customerProtocol,
    supplierProtocol,
    supplierBillingLogic,
    customerBillingLogic,
    specLCRDataList,
    customerInterfaceType,
    supplierInterfaceType,
    sourcePrime,
    destinationPrime,
    customerAirtelKamList,
    supplierAirtelKamList,
    customerInterconnect,
    supplierInterconnect,
  ]);

  // Memoized computation for roaming status
  const roamingDirectStatus = useMemo(() => {
    if (!filterState.filters) return "";
    if (filterState.filters.only_roaming) return "only_roaming";
    if (filterState.filters.only_direct) return "only_direct";
    if (filterState.filters.both) return "both";
    return "";
  }, [filterState.filters]);

  // Memoized yAxis initial value
  const initialYAxisValue = useMemo(() => {
    const vizType = filterState.graphFilters?.visualizationType;
    if (vizType === "gauge" || vizType === "scatter" || vizType === "heat") {
      return filterState.graphFilters?.yAxis || "";
    }
    return filterState.graphFilters?.yAxis || [];
  }, [
    filterState.graphFilters?.visualizationType,
    filterState.graphFilters?.yAxis,
  ]);

  const initialValues = useMemo(
    () => ({
      customer_name: [],
      customer_bind: [],
      src_prime: [],
      destination: [],
      dest_prime: [],
      supplier: [],
      supplier_name: [],
      supplier_bind: [],
      destination_operator_name: [],
      destination_country_name: [],
      customer_interface_type: [],
      supplier_interface_type: [],
      customer_billing_logic: [],
      supplier_billing_logic_multiple: [],
      customer_billing_logic_multiple: [],
      supplier_billing_logic: [],
      traffic_type_customer: [],
      traffic_type_supplier: [],
      destination_mcc_final: filterState.filters?.destination_mcc_final ?? "",
      destination_mnc_final: filterState.filters?.destination_mnc_final ?? "",
      lcr_name: [],
      spec_lcr: [],
      status: [],
      customer_kam: [],
      supplier_kam: [],
      customer_interconnect: [],
      supplier_interconnect: [],
      roamingDirectStatus,
      negative_report: filterState.filters?.negative_report || false,
      bilateral: filterState.filters?.bilateral || false,
      savePreference: filterState.graphFilters?.savePreference || "yes",
      visualizationType: filterState.graphFilters?.visualizationType || "line",
      xAxis: filterState.graphFilters?.xAxis || "",
      yAxis: initialYAxisValue,
      fieldBasedValue: filterState.graphFilters?.fieldBasedValue || "",
      bubbleSize: filterState.graphFilters?.bubbleSize || "",
      colorBy: filterState.graphFilters?.colorBy || "",
      configExpectedValue: filterState.graphFilters?.configExpectedValue || "",
      derivedFields: filterState.graphFilters?.derivedFields || "",
      mapColor: filterState.graphFilters?.mapColor || "",
      timePeriod: filterState.graphFilters?.timePeriod || "",
    }),
    [
      filterState.filters,
      filterState.graphFilters,
      roamingDirectStatus,
      initialYAxisValue,
    ]
  );

  const { mutate: reportSaveAPI } = useMutation(reportSave);

  // Memoized options
  const customerOptions = useMemo(
    () => [
      { value: " ", label: "Empty String" },
      ...(customerAirtelKamList || []),
    ],
    [customerAirtelKamList]
  );

  const supplierOptions = useMemo(
    () => [
      { value: " ", label: "Empty String" },
      ...(supplierAirtelKamList || []),
    ],
    [supplierAirtelKamList]
  );

  // Memoized field options
  const { derivedFields, tableFields, barTableFields } = useMemo(() => {
    const derived =
      props.fieldData?.data?.dataColumns?.derivedFields?.map((field) => ({
        label: field,
        value: field,
      })) || [];

    const table =
      props.fieldData?.data?.dataColumns?.tableFields
        ?.filter((field) => field !== "Date" && field !== "Datetime")
        ?.map((field) => ({
          label: field,
          value: field,
        })) || [];

    const barTable =
      props.fieldData?.data?.dataColumns?.tableFields?.map((field) => ({
        label: field,
        value: field,
      })) || [];

    return {
      derivedFields: derived,
      tableFields: table,
      barTableFields: barTable,
    };
  }, [props.fieldData?.data?.dataColumns]);

  // Clear xAxis when the currently selected xAxis is no longer present in the available options
  useEffect(() => {
    const viz = filterState.graphFilters?.visualizationType;
    const currentXAxis = filterState.graphFilters?.xAxis;

    if (!currentXAxis) return; // nothing to do

    // Build the list of valid xAxis option values based on current viz and reportName
    let optionsList = [];

    // When REPORT_PERIOD report and line/multiAxis, xAxis comes from smsFirewallViews
    if (
      (viz === "line" || viz === "multiAxis") &&
      props.reportName === FILTER_PERIOD_REPORT.reportName
    ) {
      optionsList = (smsFirewallViews || []).map((o) => (o && o.value) || o);
    } else if (viz === "line" || viz === "multiAxis") {
      // For line charts (non FILTER_PERIOD_REPORT) xAxis is a Date/Datetime label (not selectable)
      // nothing to validate against here
      return;
    } else if (viz === "scatter") {
      optionsList = (derivedFields || []).map((o) => (o && o.value) || o);
    } else {
      optionsList = (barTableFields || []).map((o) => (o && o.value) || o);
    }

    // If currentXAxis is not found in optionsList, clear it
    const isPresent = optionsList.includes(currentXAxis);
    if (!isPresent) {
      // If formik is mounted, update the field value there; otherwise update local filterState
      if (
        formikRef.current &&
        typeof formikRef.current.setFieldValue === "function"
      ) {
        try {
          formikRef.current.setFieldValue("xAxis", "");
        } catch (e) {
          setFilterState((prev) => ({
            ...prev,
            graphFilters: { ...prev.graphFilters, xAxis: "" },
          }));
        }
      } else {
        setFilterState((prev) => ({
          ...prev,
          graphFilters: { ...prev.graphFilters, xAxis: "" },
        }));
      }
    }

    // Validate and clear dependent graph fields if their selected values are no longer valid
    // Helper to clear a field via Formik or local state
    const clearField = (fieldName) => {
      if (
        formikRef.current &&
        typeof formikRef.current.setFieldValue === "function"
      ) {
        try {
          formikRef.current.setFieldValue(fieldName, "");
        } catch (e) {
          setFilterState((prev) => ({
            ...prev,
            graphFilters: { ...prev.graphFilters, [fieldName]: "" },
          }));
        }
      } else {
        setFilterState((prev) => ({
          ...prev,
          graphFilters: { ...prev.graphFilters, [fieldName]: "" },
        }));
      }
    };

    // yAxis validation
    const currentYAxis = filterState.graphFilters?.yAxis;
    if (currentYAxis) {
      // yAxis can be array (for multi/select) or object(s)
      const yOptions = (
        viz === "heat" ? barTableFields : derivedFields || []
      ).map((o) => (o && o.value) || o);
      const isYAxisValid = Array.isArray(currentYAxis)
        ? currentYAxis.every(
            (y) =>
              yOptions.includes(y) || (y && y.name && yOptions.includes(y.name))
          )
        : yOptions.includes(currentYAxis) ||
          (currentYAxis.name && yOptions.includes(currentYAxis.name));
      if (!isYAxisValid) {
        clearField("yAxis");
      }
    }

    // bubbleSize
    const currentBubble = filterState.graphFilters?.bubbleSize;
    if (currentBubble) {
      const bubbleOptions = (derivedFields || []).map(
        (o) => (o && o.value) || o
      );
      if (!bubbleOptions.includes(currentBubble)) {
        clearField("bubbleSize");
      }
    }

    // colorBy
    const currentColorBy = filterState.graphFilters?.colorBy;
    if (currentColorBy) {
      const colorOptions = (barTableFields || []).map(
        (o) => (o && o.value) || o
      );
      if (!colorOptions.includes(currentColorBy)) {
        clearField("colorBy");
      }
    }

    // derivedFields
    const currentDerived = filterState.graphFilters?.derivedFields;
    if (currentDerived) {
      const derivedOptions = (derivedFields || []).map(
        (o) => (o && o.value) || o
      );
      if (!derivedOptions.includes(currentDerived)) {
        clearField("derivedFields");
      }
    }

    // fieldBasedValue (only for line)
    const currentFieldBased = filterState.graphFilters?.fieldBasedValue;
    if (currentFieldBased) {
      const fieldBasedOptions = (tableFields || []).map(
        (o) => (o && o.value) || o
      );
      if (!fieldBasedOptions.includes(currentFieldBased)) {
        clearField("fieldBasedValue");
      }
    }
  }, [
    filterState.graphFilters?.xAxis,
    filterState.graphFilters?.visualizationType,
    filterState.graphFilters?.yAxis,
    filterState.graphFilters?.bubbleSize,
    filterState.graphFilters?.colorBy,
    filterState.graphFilters?.derivedFields,
    filterState.graphFilters?.fieldBasedValue,
    derivedFields,
    barTableFields,
    tableFields,
    props.reportName,
  ]);

  // Optimized handleSubmit with better logic separation
  const handleSubmit = useCallback(
    (values) => {
      const allFilters = [...availableFilters, ...otherFilter];

      // Normalize values
      const normalizedValues = {
        ...values,
        ...(values.destination_mcc_final && {
          destination_mcc_final: parseInt(values.destination_mcc_final, 10),
        }),
        ...(values.destination_mnc_final && {
          destination_mnc_final: parseInt(values.destination_mnc_final, 10),
        }),
      };

      // Remove "Select All" from all array fields
      Object.keys(normalizedValues).forEach((key) => {
        if (Array.isArray(normalizedValues[key])) {
          normalizedValues[key] = normalizedValues[key].filter(
            (item) => item !== "Select All"
          );
        }
      });

      const filteredValues = {};
      const graphFilters = {};

      // Filter valid values
      Object.entries(normalizedValues).forEach(([key, value]) => {
        if (isValidFilterValue(allFilters, key, value)) {
          filteredValues[key] = value;
        }
      });

      // Build graph filters
      const yAxisValues = Array.isArray(values.yAxis)
        ? values.yAxis.filter((y) => y !== "Select All")
        : values.yAxis;
      const dateValue =
        (values.visualizationType === "line" ||
          values.visualizationType === "multiAxis") &&
        props.reportName === "Dynamic Report";
      Object.assign(graphFilters, {
        visualizationType: values.visualizationType,
        xAxis: values.xAxis ? values.xAxis : dateValue ? "Datetime" : "Date",
        yAxis: yAxisValues,
        savePreference: values.savePreference,
        isGraph: true,
        ...(props.reportName === FILTER_PERIOD_REPORT.reportName && {
          timePeriod:
            values.visualizationType === "line" ||
            values.visualizationType === "multiAxis"
              ? values.xAxis
              : values.timePeriod,
        }),
      });

      // Add visualization-specific fields
      const visualizationHandlers = {
        line: () => {
          graphFilters.fieldBasedValue = values.fieldBasedValue;
        },
        scatter: () => {
          graphFilters.bubbleSize = values.bubbleSize;
          graphFilters.colorBy = values.colorBy;
        },
        gauge: () => {
          graphFilters.configExpectedValue = values.configExpectedValue;
        },
        heat: () => {
          graphFilters.derivedFields = values.derivedFields;
          graphFilters.mapColor = values.mapColor;
        },
        multiAxis: () => {
          graphFilters.secondaryYAxis = yAxisValues?.map((y) => y.name);
        },
      };

      const handler = visualizationHandlers[values.visualizationType];
      if (handler) handler();

      // Handle roaming/direct status
      const processRoamingStatus = () => {
        const appliedLabels = [];
        const statusList = normalizedValues.roamingDirectStatus || [];
        const statusMap = {
          only_roaming: "Only Roaming",
          only_direct: "Only Direct",
          both: "Roaming and Direct",
        };

        const hasRoamingDirect = Object.keys(statusMap).some((status) =>
          statusList.includes(status)
        );

        if (hasRoamingDirect || otherFilter.includes("roamingDirectStatus")) {
          Object.entries(statusMap).forEach(([key, label]) => {
            if (statusList.includes(key)) {
              appliedLabels.push(label);
              filteredValues[key] = true;
            }
          });

          if (!hasRoamingDirect) {
            appliedLabels.push("Roaming and Direct");
            filteredValues.both = true;
          }
        }

        // Add other labels
        Object.keys(filteredValues).forEach((key) => {
          if (
            key !== "roamingDirectStatus" &&
            !Object.keys(statusMap).includes(key) &&
            filterLabelMap[key]
          ) {
            appliedLabels.push(filterLabelMap[key]);
          }
        });

        return appliedLabels;
      };

      const appliedLabels = processRoamingStatus();
      if (values.savePreference === "yes" && props.activeView === "graph") {
        reportSaveAPI(
          {
            payload: {
              reportName:
                props.reportName === "Dynamic Report"
                  ? props.reportNameData
                  : props.reportName,
              graphFilters,
              filters: filteredValues,
              reportType:
                props.reportName === "Dynamic Report" ? "dynamic" : "static",
              isGraph: true,
              ...(props.reportName === "Dynamic Report" && {
                reportId: props.reportId,
              }),
            },
          },
          {
            onSuccess: () => {
              props.setLastSetGraphFilters &&
                props.setLastSetGraphFilters({
                  graphFilters,
                  filters: filteredValues,
                });
            },
            onError: () => {},
          }
        );
      }
      props.setFilters(filteredValues);
      props.setgraphFilters(graphFilters);
      props.setLabelData(appliedLabels);
      props.closeFilterDialog();
    },
    [availableFilters, otherFilter, props, reportSaveAPI]
  );

  const shouldShowFilter = useCallback(
    (filterName) => {
      return availableFilters.includes(filterName);
    },
    [availableFilters]
  );

  // Memoized validation schema
  const validationSchema = useMemo(
    () =>
      createValidationSchema(
        shouldShowFilter,
        user,
        props.activeView,
        props.graphValidation,
        props.reportName
      ),
    [
      shouldShowFilter,
      user,
      props.activeView,
      props.graphValidation,
      props.reportName,
    ]
  );

  useQuery(
    ["reportFilter", props.reportName, props.filterFlag],
    reportService.getReportFilter,
    {
      refetchOnWindowFocus: false,
      onSuccess: ({ data }) => {
        setAvailableFilters(data?.filters || []);
        setOtherFilter(data?.otherFilters || []);
      },
      onError: () => {},
    }
  );

  // Memoized chart type selection handler
  const handleChartTypeSelect = useCallback((value, setFieldValue) => {
    // First update Formik values
    setFieldValue("visualizationType", value);
    setFieldValue("xAxis", "");
    setFieldValue("yAxis", MULTI_AXIS_CHART_TYPES.includes(value) ? [] : "");

    // Clear other graph-specific fields
    const fieldsToReset = [
      "fieldBasedValue",
      "bubbleSize",
      "colorBy",
      "configExpectedValue",
      "derivedFields",
      "mapColor",
    ];
    fieldsToReset.forEach((field) => setFieldValue(field, ""));

    // Reset graphFilters + graphFilteredData as well
    setFilterState({
      graphFilters: {
        visualizationType: value,
        xAxis: "",
        yAxis: MULTI_AXIS_CHART_TYPES.includes(value) ? [] : "",
        fieldBasedValue: "",
        bubbleSize: "",
        colorBy: "",
        configExpectedValue: "",
        derivedFields: "",
        mapColor: "",
        savePreference: "yes",
      },
      filters: [],
    });
  }, []);

  // Memoized clear handler
  const handleClear = useCallback(() => {
    if (formikRef.current) {
      props.setCurrentPage && props.setCurrentPage(1);
      // formikRef.current.resetForm();
      if (
        props?.graphPreference?.filters &&
        props?.graphPreference?.graphFilters &&
        props?.activeView === "graph"
      ) {
        setFilterState(props.graphPreference);
      } else {
        formikRef.current.resetForm();
        setFilterState({ filters: [], graphFilters: {} });
      }
      setBilateralData(false);
      //  props.setDoFetch && props.setDoFetch(true);
    }
  }, [setBilateralData, props]);

  // Memoized apply handler
  const handleApply = useCallback(() => {
    if (formikRef.current) {
      const values = formikRef.current.values;

      // Validate filters using utility function with InfoModal (only for graph view)
      if (
        props.activeView !== "table" &&
        props.panelVisualizationType === "Table Report" &&
        user.isSuperAdmin &&
        !validateAndAlertFilters(
          values,
          availableFilters,
          otherFilter,
          setShowInfoModal,
          setModalMessage
        )
      ) {
        return;
      }

      props.setCurrentPage && props.setCurrentPage(1);
      formikRef.current.handleSubmit();
      props.setDoFetch && props.setDoFetch(true);
    }
  }, [
    availableFilters,
    otherFilter,
    props,
    setShowInfoModal,
    setModalMessage,
    user,
  ]);

  return (
    <div className="w-full mx-auto mt-5">
      {/* MUI Dialog */}
      <Dialog
        open={props.openFilterDialog}
        onClose={() => {
          props.closeFilterDialog();
        }}
        fullWidth
        maxWidth="md"
        aria-labelledby="filter-dialog-title"
        sx={{
          "& .MuiDialog-container": {
            "& .MuiPaper-root": {
              width: "100%",
              maxWidth: "1000px",
              minHeight: "350px",
              margin: 0,
            },
          },
        }}
      >
        {/* Graph filters */}

        <DialogTitle
          id="filter-dialog-title"
          sx={{
            m: 0,
            p: 1,
            fontSize: "14px",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
          border-b
          border-gray-200
        >
          {props.activeView === "graph"
            ? "Chart Visualization"
            : "Table Filters"}
          <button
            aria-label="close"
            onClick={props.closeFilterDialog}
            className="p-1 rounded-full hover:bg-gray-100 transition-colors duration-200 text-gray-500 hover:text-gray-700"
          >
            <CloseIcon className="w-2 h-2 text-black" />
          </button>
        </DialogTitle>

        <DialogContent dividers>
          <Formik
            initialValues={initialValues}
            onSubmit={handleSubmit}
            innerRef={formikRef}
            enableReinitialize={true}
            validationSchema={validationSchema}
            key={filterState?.filters}
          >
            {({ values, setFieldValue, errors, touched }) => (
              <Form>
                {props.activeView !== "table" &&
                  props.panelVisualizationType === "Table Report" && (
                    <div className="mb-6">
                      {/* Visualization Type */}
                      <div>
                        <InputLabel label={"Visualization type"} />

                        <div className="flex items-center gap-4 mt-2">
                          {CHART_TYPES.map(
                            ({ name, value, Icon, SelectIcon }) => (
                              <div
                                className="flex flex-col items-center"
                                key={value}
                              >
                                <div
                                  key={value}
                                  onClick={() =>
                                    handleChartTypeSelect(value, setFieldValue)
                                  }
                                  className={`flex flex-col items-center justify-center gap-1 p-2 w-20 h-16 border cursor-pointer text-center ${
                                    values.visualizationType === value
                                      ? "border-[1.5px] border-red-500 text-errorColor font-semibold"
                                      : "border-gray-300 text-gray-500 hover:border-red-300 hover:bg-red-50"
                                  }`}
                                >
                                  {values.visualizationType === value ? (
                                    <SelectIcon className={`w-8 h-8`} />
                                  ) : (
                                    <Icon className={`w-8 h-8`} />
                                  )}
                                </div>
                                <span
                                  className={`text-xs mt-1 ${
                                    values.visualizationType === value
                                      ? "text-errorColor"
                                      : "text-gray-500"
                                  }`}
                                >
                                  {name}
                                </span>
                              </div>
                            )
                          )}
                        </div>
                      </div>

                      {/* Save Preference Radio Buttons */}
                      <div className="mt-4">
                        <InputLabel
                          label={"Save Graph Preference"}
                          isMandatory={true}
                        />
                        <div className="flex gap-4 mt-2">
                          <label className="inline-flex items-center">
                            <Field
                              type="radio"
                              name="savePreference"
                              value="yes"
                              className="h-3 w-3 text-black border-gray-300 focus:ring-black accent-black"
                              style={{ accentColor: "black" }}
                            />
                            <span className="ml-2 text-sm text-gray-700">
                              Yes
                            </span>
                          </label>
                          <label className="inline-flex items-center">
                            <Field
                              type="radio"
                              name="savePreference"
                              value="no"
                              className="h-3 w-3 text-black border-gray-300 focus:ring-black accent-black"
                              style={{ accentColor: "black" }}
                            />
                            <span className="ml-2 text-sm text-gray-700">
                              No
                            </span>
                          </label>
                        </div>
                      </div>

                      {/* X and Y Axis */}
                      {CHART_TYPES_WITH_AXES.includes(
                        values.visualizationType
                      ) && (
                        <div
                          className={`grid ${
                            values.visualizationType === "scatter" ||
                            values.visualizationType === "heat"
                              ? "grid-cols-4"
                              : "grid-cols-3"
                          } gap-6 mt-4`}
                        >
                          {/* X Axis */}
                          <div>
                            <InputLabel label={"X axis"} isMandatory={true} />

                            {(values.visualizationType === "line" ||
                              values.visualizationType === "multiAxis") &&
                            props.reportName !==
                              FILTER_PERIOD_REPORT.reportName ? (
                              <div className="border border-gray-300 p-[9px] rounded-md text-sm text-gray-400">
                                Date
                              </div>
                            ) : (values.visualizationType === "line" ||
                                values.visualizationType === "multiAxis") &&
                              props.reportName ===
                                FILTER_PERIOD_REPORT.reportName ? (
                              <Select
                                name="xAxis"
                                options={smsFirewallViews}
                                placeholder={"Select X-axis"}
                                isSearchable={true}
                              />
                            ) : (
                              <Select
                                name="xAxis"
                                options={
                                  values.visualizationType === "scatter"
                                    ? derivedFields
                                    : barTableFields
                                }
                                placeholder={"Select X-axis"}
                                isSearchable={true}
                              />
                            )}
                          </div>

                          {/* Field Based Value (only for line chart) */}
                          {values.visualizationType === "line" && (
                            <div>
                              <InputLabel
                                label={"Field Based Value"}
                                isMandatory={true}
                              />
                              <Select
                                name="fieldBasedValue"
                                options={tableFields}
                                placeholder={"Select Field based Value"}
                                isSearchable={true}
                              />
                            </div>
                          )}

                          {/* Y Axis */}
                          {values.visualizationType === "pie" ||
                          values.visualizationType === "bar" ||
                          values.visualizationType === "line" ? (
                            <div>
                              <InputLabel label={"Y axis"} isMandatory={true} />
                              <CustomDropDown
                                btnWidth="w-full"
                                data={derivedFields}
                                btnName={"Select Y-Axis"}
                                onSelectionChange={(selected) =>
                                  setFieldValue("yAxis", selected)
                                }
                                value={values.yAxis}
                                defaultSelectedData={
                                  filterState.graphFilters?.yAxis || []
                                }
                              />
                              {errors.yAxis && touched.yAxis && (
                                <div className="text-red-500 text-xs">
                                  {errors.yAxis}
                                </div>
                              )}
                            </div>
                          ) : values.visualizationType === "multiAxis" ? (
                            <div>
                              <InputLabel label={"Y axis"} isMandatory={true} />
                              <MultiAxisDropdown
                                btnWidth="w-full"
                                data={derivedFields}
                                btnName={"Select Y-Axis"}
                                onSelectionChange={(event) => {
                                  let newData = [];
                                  Object.entries(event).forEach(
                                    ([key, value]) =>
                                      newData.push({ name: key, type: value })
                                  );
                                  setFieldValue("yAxis", newData);
                                }}
                                defaultSelectedData={
                                  filterState.graphFilters?.yAxis || []
                                }
                                optionsList={1}
                              />
                              {errors.yAxis && touched.yAxis && (
                                <div className="text-red-500 text-xs">
                                  {errors.yAxis}
                                </div>
                              )}
                            </div>
                          ) : values.visualizationType === "scatter" ||
                            values.visualizationType === "gauge" ||
                            values.visualizationType === "heat" ? (
                            <div>
                              <InputLabel label={"Y axis"} isMandatory={true} />
                              <Select
                                name="yAxis"
                                options={
                                  values.visualizationType === "heat"
                                    ? barTableFields
                                    : derivedFields
                                }
                                placeholder={"Select Y-axis"}
                                isSearchable={true}
                              />
                            </div>
                          ) : null}

                          {values.visualizationType === "gauge" && (
                            <div>
                              <InputLabel
                                label={"Expected Value"}
                                isMandatory={true}
                              />
                              <TextField
                                name="configExpectedValue"
                                placeholder="Enter Expected Value"
                                type="number"
                                isTouchedError={true}
                              />
                            </div>
                          )}
                          {/* Scatter Chart Extra Fields */}
                          {values.visualizationType === "scatter" && (
                            <>
                              <div>
                                <InputLabel label={"Bubble Size"} />
                                <Select
                                  name="bubbleSize"
                                  options={derivedFields}
                                  placeholder={"Select bubbleSize"}
                                  isSearchable={true}
                                />
                              </div>
                              <div>
                                <InputLabel label={"Color By"} />
                                <Select
                                  name="colorBy"
                                  options={barTableFields}
                                  placeholder={"Select Color By"}
                                  isSearchable={true}
                                />
                              </div>
                            </>
                          )}
                          {values.visualizationType === "heat" && (
                            <>
                              <div>
                                <InputLabel
                                  label={"Derived Fields"}
                                  isMandatory={true}
                                />
                                <Select
                                  name="derivedFields"
                                  options={derivedFields}
                                  placeholder={"Select Derived Fields"}
                                  isSearchable={true}
                                />
                              </div>
                              <div>
                                <InputLabel
                                  label={"Color"}
                                  isMandatory={true}
                                />
                                <Select
                                  name="mapColor"
                                  options={BASIC_COLORS.map((color) => ({
                                    value: color.value,
                                    label: color.label,
                                  }))}
                                  placeholder="Select Color"
                                  isSearchable={false}
                                  formatOptionLabel={(option) => (
                                    <div className="flex items-center gap-2">
                                      <div
                                        className="w-4 h-4 rounded border border-gray-300"
                                        style={{
                                          backgroundColor: option.value,
                                        }}
                                      />
                                      <span>{option.label}</span>
                                    </div>
                                  )}
                                />
                              </div>
                            </>
                          )}
                          {props.reportName ===
                            FILTER_PERIOD_REPORT.reportName &&
                          values.visualizationType !== "line" &&
                          values.visualizationType !== "multiAxis" ? (
                            <div>
                              <InputLabel label={"View"} isMandatory={true} />
                              <Select
                                name="timePeriod"
                                options={smsFirewallViews}
                                placeholder={"Select View"}
                                isSearchable={true}
                              />
                            </div>
                          ) : null}
                        </div>
                      )}
                    </div>
                  )}
                <div className="flex items-center gap-1">
                  {" "}
                  <InputLabel
                    label={"Select filters"}
                    isMandatory={props.activeView === "graph" ? true : false}
                  />
                  {props.activeView !== "table" && (
                    <CssTooltip
                      title={
                        <div className="text-xs">
                          {` User will be able to select ${props.configApiData?.GRAPH_FILTER_DD_LIMIT} items per drop down`}
                        </div>
                      }
                      placement="top"
                      arrow
                    >
                      <InfoIcon className="ml-2 w-4 h-3.5" />
                    </CssTooltip>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-2 rounded-md border border-gray-300 px-4 py-4">
                  {/* Customer Name */}
                  <FilterField
                    filterName="customer_name"
                    shouldShow={shouldShowFilter("customer_name")}
                    label="Customer Name"
                    isMandatory={!user?.isSuperAdmin}
                    data={customers}
                    btnName="Select Customer Name"
                    values={values}
                    setFieldValue={setFieldValue}
                    filterData={filterState.filters}
                    activeView={props.activeView}
                    panelVisualizationType={props.panelVisualizationType}
                    configApiData={props.configApiData}
                    errors={errors}
                    touched={touched}
                  />

                  {/* Customer Bind */}
                  <FilterField
                    filterName="customer_bind"
                    shouldShow={shouldShowFilter("customer_bind")}
                    label="Customer Bind"
                    data={customerBind}
                    btnName="Select Customer Bind"
                    values={values}
                    setFieldValue={setFieldValue}
                    filterData={filterState.filters}
                    activeView={props.activeView}
                    panelVisualizationType={props.panelVisualizationType}
                    configApiData={props.configApiData}
                    errors={errors}
                    touched={touched}
                  />

                  {/* Source Prime */}
                  <FilterField
                    filterName="src_prime"
                    shouldShow={shouldShowFilter("src_prime")}
                    label="Source Prime"
                    data={sourcePrime}
                    btnName="Select Source Prime"
                    values={values}
                    setFieldValue={setFieldValue}
                    filterData={filterState.filters}
                    activeView={props.activeView}
                    panelVisualizationType={props.panelVisualizationType}
                    configApiData={props.configApiData}
                    errors={errors}
                    touched={touched}
                  />

                  {/* Destination Prime */}
                  <FilterField
                    filterName="dest_prime"
                    shouldShow={shouldShowFilter("dest_prime")}
                    label="Destination Prime"
                    data={destinationPrime}
                    btnName="Select Destination Prime"
                    values={values}
                    setFieldValue={setFieldValue}
                    filterData={filterState.filters}
                    activeView={props.activeView}
                    panelVisualizationType={props.panelVisualizationType}
                    configApiData={props.configApiData}
                    errors={errors}
                    touched={touched}
                  />

                  {/* Supplier Name */}
                  <FilterField
                    filterName="supplier"
                    shouldShow={shouldShowFilter("supplier")}
                    label="Supplier Name"
                    isMandatory={!user?.isSuperAdmin}
                    data={suppliers}
                    btnName="Select Supplier Name"
                    values={values}
                    setFieldValue={setFieldValue}
                    filterData={filterState.filters}
                    activeView={props.activeView}
                    panelVisualizationType={props.panelVisualizationType}
                    configApiData={props.configApiData}
                    errors={errors}
                    touched={touched}
                    F
                  />

                  {/* Supplier Bind */}
                  <FilterField
                    filterName="supplier_bind"
                    shouldShow={shouldShowFilter("supplier_bind")}
                    label="Supplier Bind"
                    data={supplierBind}
                    btnName="Select Supplier Bind"
                    values={values}
                    setFieldValue={setFieldValue}
                    filterData={filterState.filters}
                    activeView={props.activeView}
                    panelVisualizationType={props.panelVisualizationType}
                    configApiData={props.configApiData}
                    errors={errors}
                    touched={touched}
                  />

                  {/* Destination Operator Name */}
                  <FilterField
                    filterName="destination_operator_name"
                    shouldShow={shouldShowFilter("destination_operator_name")}
                    label="Destination Operator"
                    data={destinationNameList}
                    btnName="Select Destination Operator"
                    values={values}
                    setFieldValue={setFieldValue}
                    filterData={filterState.filters}
                    activeView={props.activeView}
                    panelVisualizationType={props.panelVisualizationType}
                    configApiData={props.configApiData}
                    errors={errors}
                    touched={touched}
                  />

                  {/* Destination */}
                  <FilterField
                    filterName="destination"
                    shouldShow={shouldShowFilter("destination")}
                    label="Destination Operator"
                    data={destinationNameList}
                    btnName="Select Destination Operator"
                    values={values}
                    setFieldValue={setFieldValue}
                    filterData={filterState.filters}
                    activeView={props.activeView}
                    panelVisualizationType={props.panelVisualizationType}
                    configApiData={props.configApiData}
                    errors={errors}
                    touched={touched}
                  />

                  {/* Continue with remaining filter fields using the same pattern */}
                  <FilterField
                    filterName="destination_country_name"
                    shouldShow={shouldShowFilter("destination_country_name")}
                    label="Destination Country"
                    data={destinationCountryList}
                    btnName="Select Destination Country"
                    values={values}
                    setFieldValue={setFieldValue}
                    filterData={filterState.filters}
                    activeView={props.activeView}
                    panelVisualizationType={props.panelVisualizationType}
                    configApiData={props.configApiData}
                    errors={errors}
                    touched={touched}
                  />

                  <FilterField
                    filterName="customer_interface_type"
                    shouldShow={shouldShowFilter("customer_interface_type")}
                    label="Customer Interface"
                    data={customerInterfaceType}
                    btnName="Select Customer Interface"
                    values={values}
                    setFieldValue={setFieldValue}
                    filterData={filterState.filters}
                    activeView={props.activeView}
                    panelVisualizationType={props.panelVisualizationType}
                    configApiData={props.configApiData}
                    errors={errors}
                    touched={touched}
                  />

                  <FilterField
                    filterName="supplier_interface_type"
                    shouldShow={shouldShowFilter("supplier_interface_type")}
                    label="Supplier Interface"
                    data={supplierInterfaceType}
                    btnName="Select Supplier Interface"
                    values={values}
                    setFieldValue={setFieldValue}
                    filterData={filterState.filters}
                    activeView={props.activeView}
                    panelVisualizationType={props.panelVisualizationType}
                    configApiData={props.configApiData}
                    errors={errors}
                    touched={touched}
                  />

                  <FilterField
                    filterName="customer_billing_logic"
                    shouldShow={shouldShowFilter("customer_billing_logic")}
                    label="Customer Billing Logic"
                    data={customerBillingLogic}
                    btnName="Select Customer Billing Logic"
                    values={values}
                    setFieldValue={setFieldValue}
                    filterData={filterState.filters}
                    activeView={props.activeView}
                    panelVisualizationType={props.panelVisualizationType}
                    configApiData={props.configApiData}
                    errors={errors}
                    touched={touched}
                  />

                  <FilterField
                    filterName="customer_billing_logic_multiple"
                    shouldShow={shouldShowFilter(
                      "customer_billing_logic_multiple"
                    )}
                    label="Customer Billing Logic"
                    data={customerBillingLogic}
                    btnName="Select Customer Billing Logic"
                    values={values}
                    setFieldValue={setFieldValue}
                    filterData={filterState.filters}
                    activeView={props.activeView}
                    panelVisualizationType={props.panelVisualizationType}
                    configApiData={props.configApiData}
                    errors={errors}
                    touched={touched}
                  />

                  <FilterField
                    filterName="supplier_billing_logic"
                    shouldShow={shouldShowFilter("supplier_billing_logic")}
                    label="Supplier Billing Logic"
                    data={supplierBillingLogic}
                    btnName="Select Supplier Billing Logic"
                    values={values}
                    setFieldValue={setFieldValue}
                    filterData={filterState.filters}
                    activeView={props.activeView}
                    panelVisualizationType={props.panelVisualizationType}
                    configApiData={props.configApiData}
                    errors={errors}
                    touched={touched}
                  />

                  <FilterField
                    filterName="supplier_billing_logic_multiple"
                    shouldShow={shouldShowFilter(
                      "supplier_billing_logic_multiple"
                    )}
                    label="Supplier Billing Logic"
                    data={supplierBillingLogic}
                    btnName="Select Supplier Billing Logic"
                    values={values}
                    setFieldValue={setFieldValue}
                    filterData={filterState.filters}
                    activeView={props.activeView}
                    panelVisualizationType={props.panelVisualizationType}
                    configApiData={props.configApiData}
                    errors={errors}
                    touched={touched}
                  />

                  <FilterField
                    filterName="traffic_type_customer"
                    shouldShow={shouldShowFilter("traffic_type_customer")}
                    label="Customer Traffic Type"
                    data={customerProtocol}
                    btnName="Select Customer Traffic Type"
                    values={values}
                    setFieldValue={setFieldValue}
                    filterData={filterState.filters}
                    activeView={props.activeView}
                    panelVisualizationType={props.panelVisualizationType}
                    configApiData={props.configApiData}
                    errors={errors}
                    touched={touched}
                  />

                  <FilterField
                    filterName="traffic_type_supplier"
                    shouldShow={shouldShowFilter("traffic_type_supplier")}
                    label="Supplier Traffic Type"
                    data={supplierProtocol}
                    btnName="Select Supplier Traffic Type"
                    values={values}
                    setFieldValue={setFieldValue}
                    filterData={filterState.filters}
                    activeView={props.activeView}
                    panelVisualizationType={props.panelVisualizationType}
                    configApiData={props.configApiData}
                    errors={errors}
                    touched={touched}
                  />

                  <FilterField
                    filterName="customer_interconnect"
                    shouldShow={shouldShowFilter("customer_interconnect")}
                    label="Customer Interconnect"
                    data={customerInterconnect}
                    btnName="Select Customer Interconnect"
                    values={values}
                    setFieldValue={setFieldValue}
                    filterData={filterState.filters}
                    activeView={props.activeView}
                    panelVisualizationType={props.panelVisualizationType}
                    configApiData={props.configApiData}
                    errors={errors}
                    touched={touched}
                  />

                  <FilterField
                    filterName="supplier_interconnect"
                    shouldShow={shouldShowFilter("supplier_interconnect")}
                    label="Supplier Interconnect"
                    data={supplierInterconnect}
                    btnName="Select Supplier Interconnect"
                    values={values}
                    setFieldValue={setFieldValue}
                    filterData={filterState.filters}
                    activeView={props.activeView}
                    panelVisualizationType={props.panelVisualizationType}
                    configApiData={props.configApiData}
                    errors={errors}
                    touched={touched}
                  />

                  {/* Destination MCC */}
                  {shouldShowFilter("destination_mcc_final") && (
                    <div>
                      <InputLabel label={"Destination MCC"} />
                      <TextField
                        name="destination_mcc_final"
                        placeholder={"Enter destination MCC"}
                      />
                    </div>
                  )}

                  {/* Destination MNC */}
                  {shouldShowFilter("destination_mnc_final") && (
                    <div>
                      <InputLabel label={"Destination MNC"} />
                      <TextField
                        name="destination_mnc_final"
                        placeholder={"Enter destination MNC"}
                      />
                    </div>
                  )}

                  <FilterField
                    filterName="lcr_name"
                    shouldShow={shouldShowFilter("lcr_name")}
                    label="LCR Name"
                    data={lcrDataList}
                    btnName="Select LCR Name"
                    values={values}
                    setFieldValue={setFieldValue}
                    filterData={filterState.filters}
                    activeView={props.activeView}
                    panelVisualizationType={props.panelVisualizationType}
                    configApiData={props.configApiData}
                    errors={errors}
                    touched={touched}
                  />

                  <FilterField
                    filterName="spec_lcr"
                    shouldShow={shouldShowFilter("spec_lcr")}
                    label="Spec LCR"
                    data={specLCRDataList}
                    btnName="Select Spec LCR"
                    values={values}
                    setFieldValue={setFieldValue}
                    filterData={filterState.filters}
                    activeView={props.activeView}
                    panelVisualizationType={props.panelVisualizationType}
                    configApiData={props.configApiData}
                    errors={errors}
                    touched={touched}
                  />

                  <FilterField
                    filterName="status"
                    shouldShow={shouldShowFilter("status")}
                    label="Status"
                    data={cdrStatus}
                    btnName="Select Status"
                    values={values}
                    setFieldValue={setFieldValue}
                    filterData={filterState.filters}
                    activeView={props.activeView}
                    panelVisualizationType={props.panelVisualizationType}
                    configApiData={props.configApiData}
                    errors={errors}
                    touched={touched}
                  />

                  <FilterField
                    filterName="customer_kam"
                    shouldShow={shouldShowFilter("customer_kam")}
                    label="Customer KAM"
                    data={customerOptions}
                    btnName="Select Customer KAM"
                    values={values}
                    setFieldValue={setFieldValue}
                    filterData={filterState.filters}
                    activeView={props.activeView}
                    panelVisualizationType={props.panelVisualizationType}
                    configApiData={props.configApiData}
                    errors={errors}
                    touched={touched}
                  />

                  <FilterField
                    filterName="supplier_kam"
                    shouldShow={shouldShowFilter("supplier_kam")}
                    label="Supplier KAM"
                    data={supplierOptions}
                    btnName="Select Supplier KAM"
                    values={values}
                    setFieldValue={setFieldValue}
                    filterData={filterState.filters}
                    activeView={props.activeView}
                    panelVisualizationType={props.panelVisualizationType}
                    configApiData={props.configApiData}
                    errors={errors}
                    touched={touched}
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-4  mt-5">
                  {(otherFilter.includes("only_roaming") ||
                    otherFilter.includes("only_direct") ||
                    otherFilter.includes("both")) && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Roaming and Direct Status:
                      </label>
                      <div className="flex flex-col gap-2 mt-3">
                        {otherFilter.includes("only_roaming") && (
                          <label className="inline-flex items-center">
                            <Field
                              type="radio"
                              name="roamingDirectStatus"
                              value="only_roaming"
                              className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                            />
                            <span className="ml-2 text-sm text-gray-700">
                              Only Roaming
                            </span>
                          </label>
                        )}
                        {otherFilter.includes("only_direct") && (
                          <label className="inline-flex items-center">
                            <Field
                              type="radio"
                              name="roamingDirectStatus"
                              value="only_direct"
                              className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                            />
                            <span className="ml-2 text-sm text-gray-700">
                              Only Direct
                            </span>
                          </label>
                        )}
                        {otherFilter.includes("both") && (
                          <label className="inline-flex items-center">
                            <Field
                              type="radio"
                              name="roamingDirectStatus"
                              value="both"
                              className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                            />
                            <span className="ml-2 text-sm text-gray-700">
                              Both
                            </span>
                          </label>
                        )}
                      </div>
                    </div>
                  )}
                  {(otherFilter.includes("bilateral") ||
                    otherFilter.includes("negative_report")) && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Others:
                      </label>
                      <div className="flex flex-col gap-2 mt-2">
                        {otherFilter.includes("negative_report") && (
                          <label className="inline-flex items-center">
                            <Field
                              type="checkbox"
                              name="negative_report"
                              className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                            />
                            <span className="ml-2 text-sm text-gray-700">
                              Negative Report Required
                            </span>
                          </label>
                        )}
                        {otherFilter.includes("bilateral") && (
                          <Field name="bilateral">
                            {({ field, form }) => (
                              <label className="inline-flex items-center">
                                <input
                                  type="checkbox"
                                  {...field}
                                  checked={field.value}
                                  onChange={(e) => {
                                    const checked = e.target.checked;
                                    form.setFieldValue("bilateral", checked);
                                    setBilateralData(checked);

                                    setFieldValue(
                                      "destination_operator_name",
                                      []
                                    );
                                    setFieldValue(
                                      "destination_country_name",
                                      []
                                    );
                                    setFieldValue("destination", []);
                                  }}
                                  className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                                />
                                <span className="ml-2 text-sm text-gray-700">
                                  Include Bilateral
                                </span>
                              </label>
                            )}
                          </Field>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </Form>
            )}
          </Formik>
        </DialogContent>

        <DialogActions>
          <div className="flex gap-4 ">
            <Button
              label="Clear"
              type="button"
              buttonClassName={"w-24"}
              onClick={handleClear}
            />
            <CssTooltip
              title={
                !isMetaDataLoaded ? (
                  <div className="text-xs">Loading filter data...</div>
                ) : (
                  ""
                )
              }
              placement="top"
              arrow
            >
              <span>
                <OutlinedButton
                  label="Apply"
                  type="button"
                  buttonClassName={"w-24"}
                  onClick={handleApply}
                  disabled={!isMetaDataLoaded}
                />
              </span>
            </CssTooltip>
          </div>
        </DialogActions>
      </Dialog>

      {/* InfoModal for filter validation */}
      <InfoModal
        show={showInfoModal}
        onHide={() => setShowInfoModal(false)}
        message={modalMessage}
        btnName="OK"
      />
    </div>
  );
}
